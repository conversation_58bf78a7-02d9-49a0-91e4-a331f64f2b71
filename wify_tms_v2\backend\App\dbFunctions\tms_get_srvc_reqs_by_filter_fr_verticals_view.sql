-- DROP FUNCTION public.tms_get_srvc_reqs_by_filter_fr_verticals_view(json, int4, int4, json, text, bool, uuid);

CREATE OR REPLACE FUNCTION public.tms_get_srvc_reqs_by_filter_fr_verticals_view(requester_info json, page_no integer, page_size integer, filter_ json, search_query text, is_count_only boolean DEFAULT false, userid uuid DEFAULT NULL::uuid)
 RETURNS SETOF jsonb
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare

	_dynamic_sql text;

begin 

	_dynamic_sql = tms_get_dynamic_query_fr_srvc_reqs_by_filter_fr_verticals_view(requester_info,page_no,page_size,filter_,search_query,is_count_only,false,userid);
	return query execute _dynamic_sql;

end ;
$function$
;
