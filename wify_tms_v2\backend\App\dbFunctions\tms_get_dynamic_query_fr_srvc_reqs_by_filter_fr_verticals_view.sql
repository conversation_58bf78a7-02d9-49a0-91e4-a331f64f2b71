-- DROP FUNCTION public.tms_get_dynamic_query_fr_srvc_reqs_by_filter_fr_verticals_view(json, int4, int4, json, text, bool, bool, uuid);

CREATE OR REPLACE FUNCTION public.tms_get_dynamic_query_fr_srvc_reqs_by_filter_fr_verticals_view(requester_info json, page_no integer, page_size integer, filter_ json, search_query text, is_count_only boolean DEFAULT false, is_array_to_json boolean DEFAULT true, userid uuid DEFAULT NULL::uuid)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
-- 	Bare minimums
	status boolean;
	message text;
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;
--  Specific
	srvc_type_id_ integer;	
-- 	Filters
	filter_statuses text[];
	filter_creation_date text[];
	filter_from_date timestamp;
	filter_to_date  timestamp;
	filter_locations text[];
	filter_feedback_rxd text[];
	filter_feedback_star text[];
	filter_srvc_req_date text[];
	filter_srvc_status_category text[];
	filter_srvc_org text[];
    filter_assgn_to_prvdr_date text[];
    filter_assgn_to_prvdr_date_from timestamp;
    filter_assgn_to_prvdr_date_to timestamp;
    filter_subtsk_created text[];
    filter_priority text[];
    filter_srvc_status_title text[];
    filter_sort_by text;
    filter_sort_order text;
   
    filter_req_srvc_date text[];
    filter_req_srvc_date_from timestamp;
    filter_req_srvc_date_to timestamp;
    filter_no_of_tasks json;
    filter_is_deleted json;
    filter_is_deleted_array bool[];
    filter_srvc_type_id text[];
    filter_city text[];
    filter_state text[];   
    filter_pincode_array text[];

   	filter_srvc_req_creation_date text[];
	filter_srvc_req_from_date timestamp;
	filter_srvc_req_to_date  timestamp;
	filter_locked_for_change text[];
	filter_sp_locked_for_change text[];
	filter_verticals_list int[];
	authority_ int;
    filter_srvc_req_created_by text[];
       sp_authorities text[];
    sp_authority_fields_json json;
   	filter_vertical_id int;
    filter_subtsk_created_by_provider_today text[];
    filter_sp_first_sbtask_date text[];
    filter_sp_first_sbtask_date_from timestamp;
    filter_sp_first_sbtask_date_to timestamp;
    filter_sp_last_sbtask_date text[];
    filter_sp_last_sbtask_date_from timestamp;
    filter_sp_last_sbtask_date_to timestamp;
    first_sp_sbtsk_join text default '';
    last_sp_sbtsk_join text default '';
    filter_aging_days_spent_in text[];
    filter_aging_stages text[] default '{}'::text[];
    filter_show_all_aging text[];
	srvc_req_by_aging_filters text[];
	filter_is_gai_rated_fr_sbtsk text[];
    filter_sbtsk_gai_rating_value text[]; 
	filter_subtask_creation_date text[];
	filter_subtask_creation_date_from timestamp;
	filter_subtask_creation_date_to timestamp;

--quick assignment filters 
	filter_request_type_fr_quick_assign text[];
    filter_request_srvc_data_fr_quick_assign text[];
    filter_show_empty_req_date_fr_quick_assign bool;
    logged_user_vertical_ids int[];
	filter_request_ids int[];	
	filter_in_requests_ids bigint[];	
   

--  Temps
	matching_ids_json json;
	matching_ids bigint[];
	temp_id bigint;
	_array_for_matching_ids text[];
	_extra_where text default '';
	_dynamic_sql text;
	_select_sql text default '';
	_srvc_details json;
	_custom_fields_json json;
	_vertical_fields_json json;
	_authority_fields_json json;
	_column_key text;
	_internal_column_key text;
	_widget_type text;
	_select_mode text;
	_temp_text text;
	_value_of_filter_text text;
	_value_of_filter_array text[];
	_temp_or text default '';
	_order_by text default '';
	is_filter_sorting bool default false;
	_group_by text default '';
	_select_columns text;
	no_of_tasks_int_array int[];
    no_of_tasks_text_with_operator json;
	temp_is_having_col_added bool;
	temp_no_of_tasks_text_with_condition text;
    locked_for_change_where_clause_key text;
    org_sbtsks_config_data json;
    srvc_req_by_custom_field_search_ int[] default '{}';
    _offset text default '';
    _limit text default '';
    _extra_joins text default '';
    _tmp_text_fr_filter_gai_rated text default '';
	
	-- location based pending
	_loc_grps_data json;
	_cities text[];
	_states text[];
	_pincodes text[];
	_exlcude_cities text[];

	_start_where text;
	_having_query text default '';
	 filter_search_query text;
	 current_date_ timestamp;
	
	 --
  	 _c_by uuid;
  	
  	check_if_usr_exists_in_form_data text;
    _attachment_uploaded_key text;
   	_cust_component text;
    _is_customer_access int default 0;
	--service authorities
	 authorities_json json;
	
	show_assigned_srvc_req_only bool default false;
	show_authorized_srvc_req_only bool default false;
    has_locations_wise_access bool default false;
   
   _authorities_extra_where text default '';
   is_srvc_prvdr bool default false;
   location_ka_filter_aaya_hai boolean default false;

   	srvc_status_transition_date_join text default '';
	srvc_status_transition_filters jsonb;
  	trnstn_status_key text;
  	transtn_time_range text;
  	transtn_time_range_array text[];
  
   result_data json;
   is_initial_joins bool default true;
   _initial_joins text default '';

begin 
	status = false;
	message = 'Internal_error';

	org_id_ = json_extract_path_text(requester_info,'org_id');
	usr_id_ = json_extract_path_text(requester_info,'usr_id');
	ip_address_ = json_extract_path_text(requester_info,'ip_address');
	user_agent_ = json_extract_path_text(requester_info,'user_agent');
	srvc_type_id_ = json_extract_path_text(requester_info,'srvc_type_id');
	_is_customer_access = json_extract_path_text(requester_info,'is_customer_access');
  	current_date_ = (now() at time zone 'utc');
	is_srvc_prvdr = tms_hlpr_is_org_srvc_prvdr(org_id_);
  
  	raise notice 'requester_info by filter %', requester_info;
  
  --By default select column, group by and order by 
 	_select_columns = ' $$id$$,srvc_req.db_id ';
 
	if (requester_info->>'is_frontend')::bool is true then					
		_select_columns = ' $$id$$,srvc_req.db_id , 
							$$full_count$$, count(srvc_req.db_id) OVER() ';
	end if;
					
	_group_by = ' group by srvc_req.db_id ';
	_order_by = ' srvc_req.db_id desc ';
	_offset   = ' offset ( (' || page_no || ' - 1) * ' || page_size || ' ) ';
    _limit    = ' limit ' || page_size || ' ';
			     		
    if is_count_only is true then
    	_select_columns = ' $$full_count$$, count(distinct srvc_req.db_id) ';
    	_group_by = '';
    	_order_by ='';
    	_offset = '';
        _limit  = '';
    end if;

	authorities_json = tms_hlpr_get_srvc_type_authorities(org_id_, srvc_type_id_);
	--Get org_sbtsks_config_data
	--org_sbtsks_config_data = tms_hlpr_get_org_sbtsks_config(org_id_);
  
	filter_srvc_type_id = array( select json_array_elements_text(filter_->'srvc_type_id'))::text[];
--quick assign filter 
    filter_show_empty_req_date_fr_quick_assign = (select (filter_->>'show_empty_req_date'));
	filter_creation_date = array( select json_array_elements_text(json_extract_path(filter_,'creation_date')) )::text[];
	if cardinality(filter_creation_date) > 0 then
	 	filter_from_date =  filter_creation_date[1]::timestamp;
		filter_to_date   =  filter_creation_date[2]::timestamp;
	end if;

	filter_request_ids = array( select json_array_elements_text(filter_->'filter_request_ids'))::int[];
    if cardinality(filter_request_ids) >0 then 
    	 _extra_where := _extra_where ||
				    ' AND srvc_req.db_id NOT IN (' ||
				    array_to_string(filter_request_ids, ',') ||
				    ')';

    end if;
   
   	filter_in_requests_ids 	= array(select json_array_elements_text(filter_->'in_reqs_id'))::bigint[];
    if cardinality(filter_in_requests_ids) >0 then 
    	 _extra_where := _extra_where ||
				    ' AND srvc_req.db_id IN (' ||
				    array_to_string(filter_in_requests_ids, ',') ||
				    ')';

    end if;

	--Added new filter for srvc_req creation date
	filter_srvc_req_creation_date = array( select json_array_elements_text(json_extract_path(filter_,'creation_srvc_req_date')) )::text[];
	if cardinality(filter_srvc_req_creation_date) > 0 then
	 	filter_srvc_req_from_date =  filter_srvc_req_creation_date[1]::timestamp;
		filter_srvc_req_to_date   =  filter_srvc_req_creation_date[2]::timestamp;
	end if;

	-- Added new filter for subtask_creation_date
	filter_subtask_creation_date = array( select json_array_elements_text(json_extract_path(filter_,'subtask_creation_date')) )::text[];
	if cardinality(filter_subtask_creation_date) > 0 then
	 	filter_subtask_creation_date_from =  filter_subtask_creation_date[1]::timestamp;
		filter_subtask_creation_date_to   =  filter_subtask_creation_date[2]::timestamp;
 
	_extra_where = _extra_where || 
    ' AND (sbtsk.sbtsk_start_day >= DATE(''' || filter_subtask_creation_date_from || '''))' || 
    ' AND (sbtsk.sbtsk_start_day <= DATE(''' || filter_subtask_creation_date_to || '''))';
	end if;

	--filters priority
	filter_priority = array( select json_array_elements_text(filter_->'priority') )::text[];

   --filters filter_srvc_status_title
	filter_srvc_status_title = array( select json_array_elements_text(filter_->'srvc_status_title') )::text[];
	--vertical filter 
--    if (filter_->'verticals_list') is not null then
--    	filter_verticals_list = (array(select (filter_->'verticals_list'->>0)));
--		raise notice 'filter_verticals_list %', filter_verticals_list;
--		filter_vertical_id = filter_verticals_list[1];
--		raise notice 'filter_vertical_id %', filter_verticals_list;
--
--	end if;

	IF (filter_->'verticals_list') IS NOT NULL THEN
	    filter_verticals_list := (
	        SELECT array_agg((x)::int)
	        FROM json_array_elements_text(filter_->'verticals_list') AS x
	    );
--		raise notice 'filter_verticals_list %', filter_verticals_list;
	    filter_vertical_id := filter_verticals_list[1];  -- First element (1-based in PostgreSQL)
--		raise notice 'filter_vertical_id %', filter_verticals_list;
	END IF;
	-- Search
	if search_query !='' then
		filter_search_query = concat('%',search_query,'%');								    			
	end if;
--srvc req by search query
    srvc_req_by_custom_field_search_ = tms_hlpr_get_srvc_req_by_searchable_custom_fields(filter_verticals_list ,search_query ,org_id_);
  
	if userid IS NOT NULL then
		_extra_where = _extra_where || format(
			$SQL$
			AND EXISTS (
				SELECT 1 FROM cl_tx_cust cust
				WHERE cust.cust_id = %L::uuid
				  AND cust.org_id = %L
				  AND (
				  	-- Match by mobile number (traditional flow)
				  	(coalesce(cust.mobile_num, '') <> ''
				  	 AND coalesce(jsonb_extract_path_text(srvc_req.form_data, 'cust_mobile'), '') <> ''
				  	 AND cust.mobile_num = jsonb_extract_path_text(srvc_req.form_data, 'cust_mobile'))
				  	-- (feature flag scenario - name only)
				  	OR (coalesce(cust.full_name, '') <> ''
				  	    AND coalesce(jsonb_extract_path_text(srvc_req.form_data, 'cust_full_name'), '') <> ''
				  	    AND cust.full_name = jsonb_extract_path_text(srvc_req.form_data, 'cust_full_name'))
				  )
			)
			$SQL$,
			userid,
			org_id_
		);
	elsif search_query <> '' then
		-- Regular search (mobile number, name, etc.)
		_extra_where = _extra_where ||
			' and (
					srvc_req.display_code ilike ' || quote_literal(filter_search_query) || '
		   			or jsonb_extract_path_text(srvc_req.form_data,$$cust_full_name$$) ilike ' || quote_literal(filter_search_query) || '
		   			or jsonb_extract_path_text(srvc_req.form_data,$$request_description$$) ilike ' || quote_literal(filter_search_query) || '
					or jsonb_extract_path_text(srvc_req.form_data,$$cust_mobile$$) ilike ' || quote_literal(filter_search_query) || '
				  )';
	end if;

    if cardinality(srvc_req_by_custom_field_search_) > 0 then
		_extra_where = _extra_where ||
				'or srvc_req.db_id in ($$' || array_to_string(srvc_req_by_custom_field_search_,'$$,$$') || '$$)';
	end if;

	if cardinality(filter_srvc_type_id) > 0 then
		_extra_where = _extra_where ||
				' and srvc_req.srvc_type_id in ($$' || array_to_string(filter_srvc_type_id,'$$,$$') || '$$) ';
	end if;
		
	-- filters creation date
	if cardinality(filter_creation_date) > 0 then
		_extra_where = _extra_where ||
			' and DATE(trnstn_log.trnstn_date) >= DATE($$' || filter_from_date || '$$) ' || 
			' and DATE(trnstn_log.trnstn_date) <= DATE($$' || filter_to_date || '$$) ';
	end if;

	-- filters creation date for srvc_req
	if cardinality(filter_srvc_req_creation_date) > 0 then
		_extra_where = _extra_where ||
			' and DATE((srvc_req.c_meta).time) >= DATE($$' || filter_srvc_req_from_date || '$$) ' || 
			' and DATE((srvc_req.c_meta).time) <= DATE($$' || filter_srvc_req_to_date || '$$) ';
	end if;

--quick assign request date

    filter_request_type_fr_quick_assign = array( select (filter_->>'request_types_fr_quick_assign') )::text[];
    IF 'open' = ANY(filter_request_type_fr_quick_assign) THEN
    	_extra_where := _extra_where || ' and srvc_req.status = $$open$$';
	END IF;
	
	
   
    -- logged_user_vertical_ids := tms_hlpr_get_user_vertical_list(usr_id_);
    --  if cardinality(logged_user_vertical_ids) > 0 and _is_customer_access = 0 then 
	-- 	_extra_where = _extra_where || ' AND srvc_req.srvc_type_id = ANY($$' ||
    -- 						tms_hlpr_get_srvc_type_ids_by_verticals(logged_user_vertical_ids,org_id_)::text ||
    -- 					'$$)';
	-- end if;
	-- Status filter
	filter_statuses = array( select json_array_elements_text(json_extract_path(filter_,'statuses')) )::text[];
	filter_feedback_rxd = array( select json_array_elements_text(json_extract_path(filter_,'feedback_received')) )::text[];
	filter_feedback_star = array( select json_array_elements_text(filter_->'feedback_star') )::text[];
	filter_no_of_tasks = filter_->'no_of_tasks';
	filter_is_deleted = filter_->'is_deleted';
	filter_is_deleted_array = array( select json_array_elements_text(filter_is_deleted));
    filter_city = array( select json_array_elements_text(filter_->'cust_city'))::text[]; --city and state filter
    filter_state = array( select json_array_elements_text(filter_->'cust_state'))::text[];
	filter_pincode_array    = array(select json_array_elements_text(json_extract_path(filter_,'is_pincode')));
    filter_locked_for_change = array(select json_array_elements_text(json_extract_path(filter_,'locked_for_change')));
    filter_sp_locked_for_change = array(select json_array_elements_text(json_extract_path(filter_,'sp_locked_for_change')));
   
	--sort order by section start
	filter_sort_by   = filter_->>'sort_by'; 
	filter_sort_order   = filter_->>'sorting'; 

	if filter_sort_by is not null and filter_sort_order is not null then	
		is_filter_sorting = true;
	end if;
	
	
	if is_filter_sorting is true then	
	
		if filter_sort_by = 'creation_date' then 
			_order_by = ' (srvc_req.c_meta).time '|| filter_sort_order ||' ';
		
		elsif filter_sort_by = 'req_srvc_date' then
			_order_by = ' srvc_req.form_data->>$$request_req_date$$ '|| filter_sort_order ||' ';
		
		elsif filter_sort_by = 'statuses' then
			_order_by = ' srvc_req.status '|| filter_sort_order ||' ';
		
		elsif filter_sort_by = 'priority' then
			_order_by = ' srvc_req.priority '|| filter_sort_order ||' ';
		
		elsif filter_sort_by = 'assgn_to_prvdr_date' then
			_order_by = ' srvc_req.srvc_prvdr_assg_time '|| filter_sort_order ||' ';
		
		elsif filter_sort_by = 'srvc_status_title' then
			_order_by = ' srvc_status.title '|| filter_sort_order ||' ';
			_group_by = ' group by srvc_req.db_id, srvc_status.db_id ';
		
		elsif filter_sort_by = 'srvc_org' then
			_order_by = ' org.nickname '|| filter_sort_order ||' ';
			_group_by = ' group by srvc_req.db_id, org.org_id ';
		end if;
	
	end if;
	--sort order by sectiion end
 -- filter for srvc_req_created_by
    filter_srvc_req_created_by = array( select json_array_elements_text(filter_->'srvc_req_created_by') )::uuid[];
 --   raise notice 'filter_srvc_req_created_by %',filter_srvc_req_created_by;
	--filters for srvc prvdr dashboard
	filter_assgn_to_prvdr_date = array(select json_array_elements_text(filter_->'assgn_to_prvdr_date'))::text[];
	if cardinality(filter_assgn_to_prvdr_date) > 0 then
	 	filter_assgn_to_prvdr_date_from =  filter_assgn_to_prvdr_date[1]::timestamp;
		filter_assgn_to_prvdr_date_to   =  filter_assgn_to_prvdr_date[2]::timestamp;
	end if;

	filter_srvc_status_category = array( select json_array_elements_text(filter_->'srvc_status_category'))::text[];
	filter_srvc_org = array( select json_array_elements_text(filter_->'srvc_org'))::text[];
	if cardinality(filter_srvc_org) = 0 then
		-- apply user based filter
		filter_srvc_org = tms_get_assgnd_org_to_user(usr_id_);
	end if;

	filter_srvc_req_date = array( select json_array_elements_text(filter_->'srvc_req_date'))::text[];

    filter_subtsk_created =  array( select json_array_elements_text(filter_->'subtsk_created'))::text[]; 
    filter_subtsk_created_by_provider_today =  array( select json_array_elements_text(filter_->'subtsk_created_today'))::text[]; 
   
   --filters for Req. Service Date
	filter_req_srvc_date = array(select json_array_elements_text(filter_->'req_srvc_date'))::text[];
	if cardinality(filter_req_srvc_date) > 0  then
	 	filter_req_srvc_date_from =  filter_req_srvc_date[1]::timestamp;
		filter_req_srvc_date_to   =  filter_req_srvc_date[2]::timestamp;
	end if;

	-- filters request date
	if cardinality(filter_assgn_to_prvdr_date) > 0 then
		_extra_where = _extra_where ||
			' and DATE((srvc_req.srvc_prvdr_assg_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$ ) >= DATE(($$' || filter_assgn_to_prvdr_date_from || '$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) ' || 
			' and DATE((srvc_req.srvc_prvdr_assg_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$ ) <= DATE(($$' || filter_assgn_to_prvdr_date_to || '$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) ';
	end if;

	-- filters Req. Service Date

	if cardinality(filter_req_srvc_date) > 0 and filter_show_empty_req_date_fr_quick_assign is not true then 
		_extra_where = _extra_where ||
			' and DATE((srvc_req.form_data->>$$request_req_date$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$ ) >= DATE(($$' || filter_req_srvc_date_from || '$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) ' || 
			' and DATE((srvc_req.form_data->>$$request_req_date$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$ ) <= DATE(($$' || filter_req_srvc_date_to || '$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) ';
		
	end if;

     if filter_show_empty_req_date_fr_quick_assign is true and cardinality(filter_req_srvc_date) > 0  then 
    	 _extra_where = _extra_where ||
	        ' and ((srvc_req.form_data->>$$request_req_date$$ IS NULL) ' ||
        	' or (DATE((srvc_req.form_data->>$$request_req_date$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) >= DATE(($$' || filter_req_srvc_date_from || '$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) ' ||
        	' and DATE((srvc_req.form_data->>$$request_req_date$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= DATE(($$' || filter_req_srvc_date_to || '$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$))) ';
	
    end if;

	--filter srvc type id using verticals
	IF filter_vertical_id is not null  THEN
  		_extra_where = _extra_where || ' AND srvc_req.srvc_type_id = ANY($$' ||
    						tms_hlpr_get_srvc_type_ids_by_verticals(filter_verticals_list,org_id_)::text ||
    					'$$)';
	END IF;


--    raise notice 'filter_subtsk_created %',filter_subtsk_created;
    if 'no' = any(filter_subtsk_created) or 'no_subtask' = ANY(filter_request_type_fr_quick_assign) then
		-- _extra_where = _extra_where || 
		--     ' and ( sbtsk.db_id is null ' ||
		-- 	'       or sbtsk.org_id <> ' || org_id_ ||
		-- 	'     )' 
		-- 	;

		-- No subtask created from prvdr filter
		_extra_joins = _extra_joins || 
			'LEFT JOIN cl_tx_sbtsk AS my_org_sbtsk
			   ON my_org_sbtsk.db_id = (
				   	SELECT latest_sbtsk.db_id 
					  FROM cl_tx_sbtsk AS latest_sbtsk
					 WHERE latest_sbtsk.srvc_req_id = srvc_req.db_id
					   AND latest_sbtsk.org_id = ' || org_id_ || '
					   AND latest_sbtsk.is_deleted IS NOT TRUE
					 ORDER BY latest_sbtsk.start_time 
					 LIMIT 1
			      )';

		_extra_where = _extra_where || ' and my_org_sbtsk.db_id is null ';

	elsif 'yes' = any(filter_subtsk_created) then
		_extra_where = _extra_where || ' and sbtsk.org_id = ' || org_id_ ;
	end if;

--provider today (SP )
    if 'no' = any(filter_subtsk_created_by_provider_today) then
		  _extra_where = _extra_where ||'and sbtsk_fr_today.db_id is null'
		                               ;
		 
	elsif 'yes' = any(filter_subtsk_created_by_provider_today) then
		_extra_where = _extra_where || 'and sbtsk_fr_today.db_id is not null
                                         ';
	end if;
------
--aging filter
    filter_aging_days_spent_in = array( select json_array_elements_text(json_extract_path(filter_,'days_spent')) )::text[];
    filter_show_all_aging = array( select filter_->>'show_all' )::text[];
    if filter_->>'stages' is not null then
    	filter_aging_stages =  array( select filter_->>'stages' )::text[];
    end if;
    if cardinality(filter_aging_days_spent_in) > 0 then
        srvc_req_by_aging_filters = (select array(select json_array_elements_text(requester_info->'srvc_req_by_aging_filters'))::text[]);
        if srvc_req_by_aging_filters is null or cardinality(srvc_req_by_aging_filters) = 0 then 
         srvc_req_by_aging_filters = tms_hlpr_get_srvc_req_ids_fr_aging_filter(srvc_type_id_,filter_aging_stages,filter_aging_days_spent_in,filter_show_all_aging,org_id_);
        end if;
 		if cardinality(srvc_req_by_aging_filters)>0 then 
	    	 _extra_where = _extra_where ||
				' and srvc_req.db_id = ANY($$' ||
	    						srvc_req_by_aging_filters::text
	    					||'$$)';
		end if;			
    end if;
   -----
	if cardinality(filter_srvc_status_category) > 0 then
	    if array['unclosed', 'CLOSED'] <@ filter_srvc_status_category then 
	       _extra_where = _extra_where ||
					' and srvc_status.status_type is not null';
		elseif 'unclosed' = any(filter_srvc_status_category)then
	       _extra_where = _extra_where ||
					' and srvc_status.status_type <>$$CLOSED$$';		
		else 
			_extra_where = _extra_where ||
					' and srvc_status.status_type in ($$' || array_to_string(filter_srvc_status_category,'$$,$$') || '$$) ';
		end if;
	end if;
	if cardinality(filter_srvc_org) > 0 then
		_extra_where = _extra_where ||
				' and srvc_req.org_id in ($$' || array_to_string(filter_srvc_org,'$$,$$') || '$$) ';
	end if;
   
    if cardinality(filter_srvc_req_created_by) > 0 then
		_extra_where = _extra_where ||
				' and srvc_req.c_by in ($$' || array_to_string(filter_srvc_req_created_by,'$$,$$') || '$$) ';
	end if;

	if cardinality(filter_srvc_req_date) > 0 then
	
		if 'today' = any(filter_srvc_req_date) then
		
			_extra_where = _extra_where ||
					' and date(srvc_req.form_data->>$$request_req_date$$)  = ' || quote_literal(date(current_date_)) || '';
			
		elseif 'upcoming' = any(filter_srvc_req_date) then
		
			_extra_where = _extra_where ||
					' and date(srvc_req.form_data->>$$request_req_date$$)  > ' || quote_literal(date(current_date_)) || '';
				
		elseif 'overdue' = any(filter_srvc_req_date) then
		
			_extra_where = _extra_where ||
					' and date(srvc_req.form_data->>$$request_req_date$$)  < ' || quote_literal(date(current_date_)) || ' ';
					 
		elseif 'unspecified' = any(filter_srvc_req_date) then
		
			_extra_where = _extra_where ||
					' and date(srvc_req.form_data->>$$request_req_date$$)  is null ';
	   end if;
	end if;


	-- Get unclosed only if feedback filter is not applied
	if cardinality(filter_statuses) > 0 then
	    if  array['unclosed', 'closed'] <@ filter_statuses then 
	    _extra_where = _extra_where ||
			' and srvc_req.status is not null ';
	    elseif 'unclosed' = any(filter_statuses) then 
	    _extra_where = _extra_where ||
			'and srvc_req.status <> $$closed$$';	  
		else
		_extra_where = _extra_where ||
			' and srvc_req.status in ($$' || array_to_string(filter_statuses,'$$,$$') || '$$) ';
		end if;
	elsif cardinality(filter_feedback_rxd) <= 0 
			and cardinality(filter_feedback_star) <= 0
			and search_query = ''
			and cardinality(filter_srvc_status_title) = 0
			and cardinality(filter_srvc_status_category) = 0
			and cardinality(filter_aging_stages)=0
		 	then
		_extra_where = _extra_where ||
			' and srvc_req.status <> $$closed$$ ';
	end if;

	if cardinality(filter_priority) > 0 then
		_extra_where = _extra_where ||
			' and srvc_req.priority in ($$' || array_to_string(filter_priority,'$$,$$') || '$$) ';
	end if;
	
	if cardinality(filter_is_deleted_array) > 0 then
		_extra_where = _extra_where ||
			' and srvc_req.is_deleted = any($$'|| (filter_is_deleted_array)::text ||'$$)';
	else
		_extra_where = _extra_where ||
			 ' and srvc_req.is_deleted is not true ';
	end if;
-- raise notice 'filter_pincode_array start %',filter_pincode_array;
	if cardinality(filter_pincode_array) > 0 then
		if 'true' = any(filter_pincode_array) and 'false' = any(filter_pincode_array) or '-1' =any(filter_pincode_array) then
			_extra_where = _extra_where ;	
		
		elseif 'true' = any(filter_pincode_array) then
			_extra_where = _extra_where ||
				' and (
							srvc_req.form_data->>$$cust_pincode$$ is not null  
                  			and srvc_req.form_data->>$$cust_pincode$$ != '''' 
					  )';
			
		elseif 'false' = any(filter_pincode_array) then
			_extra_where = _extra_where ||
				' and ( 
							srvc_req.form_data->>$$cust_pincode$$ is null
				  			or srvc_req.form_data->>$$cust_pincode$$ = '''' 
					  )';
			end if;
	end if;

	if cardinality(filter_locked_for_change) > 0 then
		filter_locked_for_change = filter_locked_for_change;
	    locked_for_change_where_clause_key = 'srvc_req_lock';
	
	elseif cardinality(filter_sp_locked_for_change) > 0 then
	
		filter_locked_for_change = filter_sp_locked_for_change;
		locked_for_change_where_clause_key = 'sp_srvc_req_lock';
	end if;

	if cardinality(filter_locked_for_change) > 0 then
		if 'true' = any(filter_locked_for_change) and 'false' = any(filter_locked_for_change) or '-1' =any(filter_locked_for_change) then
			_extra_where = _extra_where ;	
		
		elseif 'true' = any(filter_locked_for_change) then
			_extra_where = _extra_where ||
				' and (
							srvc_req.form_data->>$$' || locked_for_change_where_clause_key || '$$ is not null  
                  			and (srvc_req.form_data->>$$' || locked_for_change_where_clause_key || '$$)::bool is true 
					  )';
			
		elseif 'false' = any(filter_locked_for_change) then
			_extra_where = _extra_where ||
				' and ( 
							srvc_req.form_data->>$$' || locked_for_change_where_clause_key || '$$ is null
				  			or (srvc_req.form_data->>$$' || locked_for_change_where_clause_key || '$$)::bool is false 
					  )';
			end if;
	end if;

   if cardinality(filter_srvc_status_title) > 0 then
		_extra_where = _extra_where ||
			' and srvc_status.title in ($$' || array_to_string(filter_srvc_status_title,'$$,$$') || '$$) ';
	end if;

	-- Locations filter
	filter_locations = array( select json_array_elements_text(json_extract_path(filter_,'locations')) )::text[];
--	raise notice 'filter_locations %',filter_locations;

	if cardinality(filter_locations) = 0 then
		-- apply user based filter
		filter_locations = tms_get_assigned_loc_to_user(usr_id_);
		_c_by = usr_id_;
--		raise notice 'filter_locations %',filter_locations;
	else 
		location_ka_filter_aaya_hai = true;
	end if;
	
	/* I have added "c_by" in the form_data below because, 
		for example, if Tufel has created a ticket and the "Show authorized service requests only" flag in the role configuration is set to true, 
		then this ticket should be visible to Tufel.
		However, if Soumya makes any changes, the "usr_id" in its form_data gets changed after that this ticket is not visible for tufel and our
		requirement who created the ticket should be able to see it.
	 */
--	check_if_usr_exists_in_form_data = 'EXISTS (SELECT * FROM jsonb_each_text(srvc_req.form_data || (json_build_object($$c_by$$,srvc_req.c_by))::jsonb) as srvc_req_data  WHERE srvc_req_data.value = '|| quote_literal(usr_id_) || ')';
--	check_if_usr_exists_in_form_data = 'EXISTS (
--		select * 
--			from json_array_elements_text('|| quote_literal(authorities_json) ||')
--			where srvc_req.form_data->>value = '|| quote_literal(usr_id_) || '
--			or
--			srvc_req.c_by = '|| quote_literal(usr_id_) || '
--			or
--           ' || quote_literal(usr_id_) || '  =  any(sbtsk.assigned_to) and sbtsk.is_deleted is not true 
--	)';

	if is_srvc_prvdr then

		check_if_usr_exists_in_form_data = ' ( 
			srvc_req.c_by = '|| quote_literal(usr_id_) || '
            or
			' || quote_literal(usr_id_) || '  =  any(sbtsk.assigned_to) and sbtsk.is_deleted is not true 
	        or
			' || quote_literal(usr_id_) || '  =  any(srvc_req.prvdr_authorities)
		)';
	else
		check_if_usr_exists_in_form_data = ' (
			srvc_req.c_by = '|| quote_literal(usr_id_) || '
            or
			' || quote_literal(usr_id_) || '  =  any(sbtsk.assigned_to) and sbtsk.is_deleted is not true 
	        or
			' || quote_literal(usr_id_) || '  =  any(srvc_req.brand_authorities)
		)';
	end if;

	show_assigned_srvc_req_only = ( json_array_length(tms_hlpr_usr_has_access_to_srvc_requests(usr_id_,org_id_)) > 0 );
    show_authorized_srvc_req_only = ( json_array_length(tms_hlpr_usr_has_authority_fr_srvc_requests(usr_id_,org_id_)) > 0 );
    has_locations_wise_access = ( cardinality(filter_locations) > 0 );
   
   	if location_ka_filter_aaya_hai is true then
			if show_authorized_srvc_req_only or show_assigned_srvc_req_only then
				_extra_where = _extra_where ||
						' and (' 
								|| check_if_usr_exists_in_form_data ||' 
								and tms_hlpr_match_loc_grp_of_srvc_req(srvc_req, ' || quote_literal(filter_locations) ||' , ' || quote_literal(org_id_)  ||' , ' ||  location_ka_filter_aaya_hai  ||' )
							)';
			else 
				_extra_where = _extra_where || 
		    		' and ' || ' tms_hlpr_match_loc_grp_of_srvc_req(srvc_req, ' || quote_literal(filter_locations) ||' , ' || quote_literal(org_id_)  ||' , ' ||  location_ka_filter_aaya_hai  ||' ) ';
			end if;
			
	else
		if show_authorized_srvc_req_only or show_assigned_srvc_req_only then
   	
	   	_extra_where = _extra_where || 
		    		' and ' || check_if_usr_exists_in_form_data;
   			
		elseif has_locations_wise_access and location_ka_filter_aaya_hai is false  then
		
			_extra_where = _extra_where ||
						' and (' 
								|| check_if_usr_exists_in_form_data ||' 
								or tms_hlpr_match_loc_grp_of_srvc_req(srvc_req, ' || quote_literal(filter_locations) ||' , ' || quote_literal(org_id_)  ||' , ' ||  location_ka_filter_aaya_hai  ||' )
							)';
		end if;
	end if;
   	
--	if cardinality(filter_locations) > 0 then
		-- Location based pending
--		_loc_grps_data = tms_get_city_state_ex_city_from_grps(filter_locations::integer[]);
----		raise notice '_loc_grps_data %',_loc_grps_data;
--	
--		_cities = array( select json_array_elements_text(_loc_grps_data->'cities'));
--		_states = array( select json_array_elements_text(_loc_grps_data->'states'));
--		_exlcude_cities = array( select json_array_elements_text(_loc_grps_data->'exlcude_cities'));
--		_pincodes = array( select json_array_elements_text(_loc_grps_data->'pincode'));
	
--		raise notice '_extra_where %',_extra_where;
--		raise notice '_states %',_states;
--		raise notice '_exlcude_cities %',_exlcude_cities;
		
--			_extra_where = _extra_where || 
--					' and tms_match_loc_of_srvc_req(loc_mstr,$${' ||array_to_string(_cities,',') || '}$$,' ||
--						'$${' || array_to_string(_states,',') || '}$$,' ||
--					'$${' || array_to_string(_exlcude_cities,',') || '}$$, $$'||  COALESCE(_c_by::text,'') || 
--					'$$ , srvc_req, $${' ||array_to_string(_pincodes,',') || '}$$ ) ';

--		if json_array_length(tms_hlpr_usr_has_authority_fr_srvc_requests(usr_id_,org_id_)) = 0 then
--			_extra_where = _extra_where ||
--					' and (' 
--							 || check_if_usr_exists_in_form_data ||' 
--							 or tms_hlpr_match_loc_grp_of_srvc_req(srvc_req, ' || quote_literal(filter_locations) ||' , ' || quote_literal(org_id_)  ||' )
--						  )';
--		end if;
--	end if;

--	raise notice '_extra_where  %',_extra_where;

	-- Dynamic custom fields filter
	select srvc_type.form_data 
	  from cl_cf_service_types as srvc_type 
	 where (
	 		  	_is_customer_access = 1
			 	or org_id = org_id_
		   )
	   and service_type_id = srvc_type_id_ 
	  into _srvc_details;
	 	 
	if length(_srvc_details->>'srvc_cust_fields_json') > 0 then 
		_custom_fields_json = _srvc_details#>>'{srvc_cust_fields_json}' ;
		_custom_fields_json = _custom_fields_json#>>'{translatedFields}';
	end if;

	if length(_srvc_details->>'srvc_authorities') > 0 then 
		_authority_fields_json = array_to_json(array(
			 select json_build_object(
			 			'key','authority_' || value,
			 			'widget','select',
						'widgetProps',json_build_object(
							'mode','multiple'
						)
			 		)
			   from json_array_elements_text(_srvc_details->'srvc_authorities')
		));
	
		if(_custom_fields_json is null) then
			_custom_fields_json = _authority_fields_json::jsonb;
		else
			_custom_fields_json = _custom_fields_json::jsonb || _authority_fields_json::jsonb;
		end if;
	end if;
--srvc prvdr authority filter
	if is_srvc_prvdr then
--	raise notice 'tms_hlpr_is_org_srvc_prvdr(org_id_) %',tms_hlpr_is_org_srvc_prvdr(org_id_) ;
		select array_agg (distinct role_id_)
		  from (
			  	select unnest(array(select json_array_elements_text(sp_authorities.settings_data->'authority_id'))::int[]) as role_id_
				  from cl_tx_orgs_settings as sp_authorities
				 where sp_authorities.org_id = org_id_
			   ) as foo
		  into sp_authorities;
		  --	raise notice 'sp_authorities %',sp_authorities ;	 
	 	if cardinality(sp_authorities) > 0 then 
			sp_authority_fields_json = array_to_json(array(
				 select json_build_object(
				 			'key','authority_' || value,
				 			'widget','select',
							'widgetProps',json_build_object(
								'mode','multiple'
							)
				 		)
				   from  unnest(sp_authorities) as value
			));
	--	raise notice 'sp_authority_fields_json %',sp_authority_fields_json;
			if(_custom_fields_json is null) then
				_custom_fields_json = sp_authority_fields_json::jsonb;
			else
				_custom_fields_json = _custom_fields_json::jsonb || sp_authority_fields_json::jsonb;
			end if;
		
		end if;
		
		-- CHeck if vertical is slected in filter
		-- Get the filter vertical id
	    -- Get the custom fields for the vertical id
		-- append them to _custom_fields_json
		if cardinality(filter_verticals_list) > 0 then
  			-- filter_vertical_id = filter_verticals_list[1];
  		
  			if filter_vertical_id > 0 then
  			
				_vertical_fields_json = (tms_get_sp_custom_fields_details(requester_info,filter_vertical_id)->'data'->'form_data');
				_vertical_fields_json = _vertical_fields_json#>>'{sp_cust_fields_json}';
				_vertical_fields_json = _vertical_fields_json#>>'{translatedFields}';

			end if;
		
			if(_custom_fields_json is null) then
				_custom_fields_json = _vertical_fields_json::jsonb;
			else
				_custom_fields_json = _custom_fields_json::jsonb || _vertical_fields_json::jsonb;
			end if;
		end if;
	
	end if;
	
	if(_custom_fields_json is not null) then
	
		FOR single_id_index IN 0..json_array_length(_custom_fields_json) - 1 loop
		
		  	_column_key = _custom_fields_json->single_id_index#>>'{key}';
		  	_widget_type = _custom_fields_json->single_id_index#>>'{widget}';
		  	_select_mode = _custom_fields_json->single_id_index->'widgetProps'#>>'{mode}';
		  	_internal_column_key = ' srvc_req.form_data#>>$${' || _column_key || '}$$' ;
		    _cust_component 	 = _custom_fields_json->single_id_index#>>'{cust_component}';
		  	if filter_->_column_key is not null then
		  		
		  		if (_widget_type = 'select' and _select_mode = 'multiple') or _widget_type = 'checkbox-group' then 
		  			if json_array_length(filter_->_column_key) > 0 then 
			  			_value_of_filter_array = array( select json_array_elements_text(filter_->_column_key) )::text[];
			  			_internal_column_key = ' srvc_req.form_data->' || quote_literal(_column_key);

			  		    if '-2' = ANY(_value_of_filter_array) and cardinality(_value_of_filter_array) > 1 then
						    _extra_where = _extra_where ||
				    			        ' and (
												(
													jsonb_typeof(' || _internal_column_key::text || ') = $$array$$ and
													jsonb_array_length(' || _internal_column_key::text || ') = 0 
												)
												or
												(
													NOT (srvc_req.form_data ?' || quote_literal(_column_key) || ')' 
													' or ' || _internal_column_key || ' ?| array[$$' || array_to_string(_value_of_filter_array,'$$,$$') || '$$] 
													or ' || _internal_column_key::text || ' is null 
													or ' || _internal_column_key::text || ' = '|| quote_literal('""') ||'
												)
											)';
							elseif 
								'-2' = ANY(_value_of_filter_array) then
						    _extra_where = _extra_where ||
				    			        ' and (
												(
													jsonb_typeof(' || _internal_column_key::text || ') = $$array$$ and
													jsonb_array_length(' || _internal_column_key::text || ') = 0 
												)
												or
												(
													NOT (srvc_req.form_data ?' || quote_literal(_column_key) || ')
													or ' || _internal_column_key::text || ' is null
													or ' || _internal_column_key::text || ' = '|| quote_literal('""') ||'
												)
											)'; 
							else
					        _extra_where = _extra_where ||
					            ' and ' || _internal_column_key || ' ?| array[$$' || array_to_string(_value_of_filter_array,'$$,$$') || '$$] ';
					    end if;
		  			end if;
		  		elseif _widget_type = 'date-picker' then
			  			_value_of_filter_text = json_extract_path_text(filter_,_column_key);
			  			_extra_where = _extra_where ||
			  					' and date('|| _internal_column_key ||') = ' || quote_literal(date(_value_of_filter_text));
				else
		  			_value_of_filter_text = json_extract_path_text(filter_,_column_key);

					if '-2' = _value_of_filter_text then
			    			_extra_where = _extra_where ||
				    			        ' and ((' || _internal_column_key || ') is null or NOT (srvc_req.form_data ?' || quote_literal(_column_key) || '))' ;
							else
		  			_extra_where = _extra_where ||
						' and ' || _internal_column_key || ' = ' || quote_literal(_value_of_filter_text);
					end if;
		  		end if;		  	
		  		--order by for custom fields
			  	if is_filter_sorting is true then	
					if filter_sort_by = _column_key then 
						_order_by =  _internal_column_key ||' '|| filter_sort_order ||' ';
					end if;
				end if;

		  	end if;
		  	if _cust_component = 'Files' then
		  		--For attachment uploaded
				_attachment_uploaded_key = (_column_key || '_uploaded');
				if _attachment_uploaded_key is not null and filter_->>_attachment_uploaded_key is not null then
					_extra_where = _extra_where || ' and tms_hlpr_srvc_req_attachment_data_is_exists( srvc_req.form_data, $$'|| _column_key ||'$$) = '||  quote_literal(filter_->>_attachment_uploaded_key) ||' ';
				end if;
				--If we want to apply any other filter of attachments, then write the code here.
		  	end if;
	    END LOOP;
   	end if;
   
--    raise notice '_order_by %',_order_by;
  
   	if _order_by != '' then
   		_order_by =  'order by '|| _order_by ||' ' ;
   	end if;
 
   	-- Feedback filter
	
--	raise notice 'filter_feedback_rxd %',filter_feedback_rxd;
	if 'true' = any(filter_feedback_rxd) then
		_extra_where = _extra_where ||
			' and srvc_req.form_data->$$feedback_data$$ is not null ';
	elsif 'false' = any(filter_feedback_rxd) then
		_extra_where = _extra_where ||
			' and srvc_req.form_data->$$feedback_data$$ is null ';
	end if;
	
	
--	raise notice 'filter_feedback_star %',filter_feedback_star;
	if cardinality(filter_feedback_star) > 0 then
		_extra_where = _extra_where ||
			' and srvc_req.form_data->$$feedback_data$$->$$form_data$$->>(srvc_req.form_data->$$feedback_data$$->>$$rating_field_key$$)'      --|| quote_literal(_rate_field_key)
			|| ' = ANY(array[$$' || array_to_string(filter_feedback_star,'$$,$$') || '$$]) ';
	end if;

	if filter_no_of_tasks is not null then
		_having_query = ' HAVING ';
		 --integer entry seprate
		no_of_tasks_int_array = array(
			select value
			  from jsonb_array_elements(filter_no_of_tasks::jsonb)
			 where value::text NOT LIKE '%"%' 
		);
		if cardinality(no_of_tasks_int_array) > 0 then 
			_having_query = _having_query || 
				'count(sbtsk.db_id) = any(array[' || array_to_string(no_of_tasks_int_array,',') || '])';
			temp_is_having_col_added = true;
		end if;
	
	    --conditional entry seprate
		no_of_tasks_text_with_operator = array_to_json(array(
			 select value
			   from json_array_elements(filter_no_of_tasks)
			  where value::text LIKE '%"%' 
		));
--		raise notice 'no_of_tasks_text_with_operator %', no_of_tasks_text_with_operator;
		 
		FOR single_req_task_count_indx IN 0..json_array_length(no_of_tasks_text_with_operator) - 1 loop
			if temp_is_having_col_added is true then
				_having_query = _having_query || ' or ';
			end if;
		    temp_no_of_tasks_text_with_condition = no_of_tasks_text_with_operator->>single_req_task_count_indx;
--			TODO vulnerable to SQL injection
		    _having_query = _having_query || 'count(sbtsk.db_id) ' || temp_no_of_tasks_text_with_condition;
		end loop;
	end if;

	if srvc_type_id_ = 0 or requester_info->>'is_customer_access' = '1' then
		-- This is a service provider call
		_start_where = 'srvc_req.srvc_prvdr = ' || quote_literal(org_id_);
		if requester_info->>'is_customer_access' = '1' then
			_start_where = _start_where || ' and srvc_req.srvc_type_id = ' || quote_literal(srvc_type_id_);	
		end if;
	else
		_start_where = 'srvc_req.org_id = ' || quote_literal(org_id_) || 
		 ' and srvc_req.srvc_type_id = ' || quote_literal(srvc_type_id_);
	end if;

--city and state filter -----------
   if cardinality(filter_city)>0 then 
  -- 	raise notice 'filter_city%', filter_city;
        _extra_where = _extra_where ||
			' and UPPER(trim(srvc_req.form_data->>$$cust_city$$)) = any(array[$$' || UPPER(array_to_string(filter_city,'$$,$$')) || '$$]) ' ;   		
   end if;
	   	
   if cardinality(filter_state)>0 then 
  -- 	raise notice 'filter_city%', filter_city;
        _extra_where = _extra_where ||
			' and UPPER(trim(srvc_req.form_data->>$$cust_state$$)) = any(array[$$' || UPPER(array_to_string(filter_state,'$$,$$')) || '$$]) '  ;   		
   end if;
  
  	
  
--	if json_array_length(tms_hlpr_usr_has_access_to_srvc_requests(usr_id_,org_id_)) > 0
--  	   and json_array_length(tms_hlpr_usr_has_authority_fr_srvc_requests(usr_id_,org_id_)) > 0 then
----  	   _extra_where = _extra_where || 	  
----		  ' and (
----					(
----						' || quote_literal(usr_id_) || '  =  any(sbtsk.assigned_to) and sbtsk.is_deleted is not true
----					)
----					or
----					(
----			 		   	' || check_if_usr_exists_in_form_data ||'
----				    )
----			    )';
--
--  	   	_extra_where = _extra_where || 
--	    		' and ' || check_if_usr_exists_in_form_data;
--		
--	elseif json_array_length(tms_hlpr_usr_has_access_to_srvc_requests(usr_id_,org_id_)) > 0 then
----        	 _extra_where = _extra_where || 
----        		' and (
----						 (
----							' || quote_literal(usr_id_) || '  =  any(sbtsk.assigned_to) and sbtsk.is_deleted is not true 
----						 )
----						 or
----                         (
----							 srvc_req.c_by = '|| quote_literal(usr_id_) || '
----						 )
----					  )
----				';
--
--			_extra_where = _extra_where || 
--	    		' and ' || check_if_usr_exists_in_form_data;
--        	
--    elseif json_array_length(tms_hlpr_usr_has_authority_fr_srvc_requests(usr_id_,org_id_)) > 0 then
--	   	 	_extra_where = _extra_where || 
--	    		' and ' || check_if_usr_exists_in_form_data;
--    end if;
   
-- first sp sbtask filter
   	filter_sp_first_sbtask_date = array( select json_array_elements_text(json_extract_path(filter_,'sp_first_task_date')) )::text[];
	if cardinality(filter_sp_first_sbtask_date) > 0 then
	 	filter_sp_first_sbtask_date_from =  filter_sp_first_sbtask_date[1]::timestamp;
		filter_sp_first_sbtask_date_to   =  filter_sp_first_sbtask_date[2]::timestamp;
	  -- Set join condition
	  first_sp_sbtsk_join := ' LEFT JOIN cl_tx_sbtsk AS first_sp_sbtsk
						         ON first_sp_sbtsk.db_id = (
									     SELECT earliest_sbtsk.db_id 
									       FROM cl_tx_sbtsk AS earliest_sbtsk
									      WHERE earliest_sbtsk.srvc_req_id = srvc_req.db_id
									        AND earliest_sbtsk.org_id = srvc_req.srvc_prvdr	
									        AND earliest_sbtsk.is_deleted IS NOT TRUE
									      ORDER BY earliest_sbtsk.start_time 
									      LIMIT 1
						    )';
	  _extra_where = _extra_where ||
				' and DATE(first_sp_sbtsk.start_time) >= DATE($$' ||filter_sp_first_sbtask_date_from  || '$$) ' || 
				' and DATE(first_sp_sbtsk.start_time) <= DATE($$' || filter_sp_first_sbtask_date_to || '$$) ';

	end if;

-- last sp sbtask filter
   	filter_sp_last_sbtask_date = array( select json_array_elements_text(json_extract_path(filter_,'sp_last_task_date')) )::text[];
	if cardinality(filter_sp_last_sbtask_date) > 0 then
	 	filter_sp_last_sbtask_date_from =  filter_sp_last_sbtask_date[1]::timestamp;
		filter_sp_last_sbtask_date_to   =  filter_sp_last_sbtask_date[2]::timestamp;
	 -- Set join condition
	  last_sp_sbtsk_join := ' left join cl_tx_sbtsk as last_sp_sbtsk 
	        				    on last_sp_sbtsk.db_id = (
			 				      		select latest_sbtsk.db_id 
							      		  from cl_tx_sbtsk as latest_sbtsk
							      		 where latest_sbtsk.srvc_req_id = srvc_req.db_id
										   and latest_sbtsk.org_id = srvc_req.srvc_prvdr
										   and latest_sbtsk.is_deleted is not true
							       		 order by latest_sbtsk.start_time desc
							       		 limit 1
								     )';
	  _extra_where = _extra_where ||
			' and DATE(last_sp_sbtsk.start_time) >= DATE($$' ||filter_sp_last_sbtask_date_from  || '$$) ' || 
			' and DATE(last_sp_sbtsk.start_time) <= DATE($$' || filter_sp_last_sbtask_date_to || '$$) ';

	
	end if;

	srvc_status_transition_filters = tms_hlpr_get_status_transition_date_filter_objs(filter_::jsonb);

	if srvc_status_transition_filters is not null AND srvc_status_transition_filters <> '{}'  then
		 FOR trnstn_status_key, transtn_time_range IN SELECT key, value FROM jsonb_each(srvc_status_transition_filters)
		 loop

		    SELECT ARRAY(SELECT jsonb_array_elements_text(transtn_time_range::jsonb)) INTO transtn_time_range_array;
		     srvc_status_transition_date_join := srvc_status_transition_date_join || 'inner join cl_tx_srvc_req_trnstn_log as txn_'||trnstn_status_key ||'
												     on txn_'||trnstn_status_key ||'.srvc_req_id = srvc_req.db_id
												    and txn_'||trnstn_status_key ||'.status_key = '|| quote_literal(trnstn_status_key) ||'
												    and Date((txn_'||trnstn_status_key||'.trnstn_date)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) >= Date(($$'|| transtn_time_range_array[1]::timestamp ||'$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)
												    and Date((txn_'||trnstn_status_key||'.trnstn_date)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$) <= Date(($$'|| transtn_time_range_array[2]::timestamp ||'$$)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$)
		     									 ';
		 END LOOP;
	end if;
  
	filter_is_gai_rated_fr_sbtsk = array( select json_array_elements_text(json_extract_path(filter_,'is_gai_rated_fr_sbtsk')) )::text[];
	if '-1' = any(filter_is_gai_rated_fr_sbtsk) then
		filter_is_gai_rated_fr_sbtsk = '{}'::text[];
	end if;
	if cardinality(filter_is_gai_rated_fr_sbtsk) > 0 then
		if 'true' = any(filter_is_gai_rated_fr_sbtsk) then
			_tmp_text_fr_filter_gai_rated = ' is not null '; -- If atleast one task is rated
		elsif 'false' = any(filter_is_gai_rated_fr_sbtsk) then
			_tmp_text_fr_filter_gai_rated = ' is null '; -- If atleast one task is not rated
		end if;
		_extra_joins = _extra_joins || 
			'INNER JOIN cl_tx_sbtsk as gai_rated_sbtsk 
		        on gai_rated_sbtsk.db_id = (
                    select db_id
					  from cl_tx_sbtsk as gai_sbtsk_fr_id
					 where gai_sbtsk_fr_id.srvc_req_id = srvc_req.db_id 	
					   and gai_sbtsk_fr_id.is_deleted is not true
                       and gai_sbtsk_fr_id.form_data->$$gai_rating$$ ' || _tmp_text_fr_filter_gai_rated || '
                       and case
            	   			when srvc_req.srvc_prvdr = '|| org_id_ ||' THEN gai_sbtsk_fr_id.org_id = '|| org_id_ ||'
            	   			else true 
     		   			   end 
				     limit 1
                   )';
	end if;
   
    filter_sbtsk_gai_rating_value = array( select json_array_elements_text(filter_->'sbtsk_gai_rating') )::text[]; 
   
    if '-1' = any(filter_is_gai_rated_fr_sbtsk) then
		filter_sbtsk_gai_rating_value = '{}'::text[];
	end if;

    if cardinality(filter_sbtsk_gai_rating_value) > 0 then 
   		    _extra_joins = _extra_joins ||
   		    	'INNER JOIN cl_tx_sbtsk as gai_rated_sbtsk_value 
		            on gai_rated_sbtsk_value.db_id = (
	                    select db_id
						  from cl_tx_sbtsk as gai_sbtsk_fr_id
						 where gai_sbtsk_fr_id.srvc_req_id = srvc_req.db_id 	
						   and gai_sbtsk_fr_id.is_deleted is not true
	                       and gai_sbtsk_fr_id.form_data->$$gai_rating$$->$$gemini$$->>$$rating$$ = any($$' ||filter_sbtsk_gai_rating_value::text  || '$$) 
	                     limit 1
					)';

    end if;

	-- Add conditional joins if filter_request_type_fr_quick_assign is not 'all' & 'no_subtask
    if filter_->>'request_types_fr_quick_assign' is not null and 
    	filter_->>'for_gen_auto_assgn_lambda' is null then
    	is_initial_joins = false;
    end if;

	IF is_initial_joins THEN
		_initial_joins = _initial_joins || 
			    ' inner join cl_tx_srvc_req_trnstn_log as trnstn_log
			         on trnstn_log.srvc_req_id = srvc_req.db_id
			        and trnstn_log.status_key = ' || quote_literal('open') ||'
			      inner join cl_cf_srvc_statuses as srvc_status
			         on srvc_status.status_key = srvc_req.status
			        and srvc_status.srvc_id = srvc_req.srvc_type_id
			      inner join cl_tx_orgs as org
			         on org.org_id = srvc_req.org_id
			    ';
	end if;

    if is_array_to_json then 
        _select_sql = ' select array_to_json(array( ';
    end if;

	_dynamic_sql = 
		' '|| _select_sql ||' 
		select jsonb_build_object( 
			   		'|| _select_columns ||'
			   )
	      from public.cl_tx_srvc_req srvc_req 
           	   '|| _initial_joins ||' 
		  left join cl_tx_sbtsk as sbtsk
			on sbtsk.srvc_req_id = srvc_req.db_id
		   and sbtsk.is_deleted is not true
		  left join cl_tx_sbtsk as sbtsk_fr_today
			on sbtsk_fr_today.db_id = (
					select db_id
					from cl_tx_sbtsk
					where srvc_req_id = srvc_req.db_id
						and date(start_time) = ' || quote_literal(date(current_date_)) ||'
						and is_deleted is not true
						and org_id = '|| org_id_ ||'
					limit 1
				)
           '||first_sp_sbtsk_join || last_sp_sbtsk_join|| srvc_status_transition_date_join ||
            _extra_joins ||'

		 where ' || _start_where ||
		 _extra_where ||
	     _group_by ||
	     _having_query || 
	     _order_by ||
		 _limit || 
		 _offset || '  
		';

    if is_array_to_json then
        _dynamic_sql = ' ' || _dynamic_sql || ' ))';
    end if;
	raise notice 'Final sql %',_dynamic_sql;
	return _dynamic_sql;

end ;
$function$
;
