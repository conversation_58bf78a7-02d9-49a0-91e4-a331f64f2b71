-- Test script to verify the duplicate label handling logic
DO $$
DECLARE
    _label_count_map json := '{}';
    _current_label_count integer;
    _unique_col_name text;
    _single_col_name text;
    _single_col_key text;
BEGIN
    -- Test case 1: First occurrence of "Client ID"
    _single_col_name := 'Client ID';
    _single_col_key := 'key1';
    
    _current_label_count := COALESCE((_label_count_map->>_single_col_name)::integer, 0) + 1;
    _label_count_map := _label_count_map || jsonb_build_object(_single_col_name, _current_label_count);
    
    if _current_label_count = 1 then
        _unique_col_name := _single_col_name;
    else
        _unique_col_name := _single_col_name || ' (' || _single_col_key || ')';
    end if;
    
    RAISE NOTICE 'Test 1 - First occurrence: Label=%, Key=%, Unique Name=%, Count=%', 
        _single_col_name, _single_col_key, _unique_col_name, _current_label_count;
    
    -- Test case 2: Second occurrence of "Client ID"
    _single_col_name := 'Client ID';
    _single_col_key := 'key2';
    
    _current_label_count := COALESCE((_label_count_map->>_single_col_name)::integer, 0) + 1;
    _label_count_map := _label_count_map || jsonb_build_object(_single_col_name, _current_label_count);
    
    if _current_label_count = 1 then
        _unique_col_name := _single_col_name;
    else
        _unique_col_name := _single_col_name || ' (' || _single_col_key || ')';
    end if;
    
    RAISE NOTICE 'Test 2 - Second occurrence: Label=%, Key=%, Unique Name=%, Count=%', 
        _single_col_name, _single_col_key, _unique_col_name, _current_label_count;
    
    -- Test case 3: Different label
    _single_col_name := 'Customer Name';
    _single_col_key := 'key3';
    
    _current_label_count := COALESCE((_label_count_map->>_single_col_name)::integer, 0) + 1;
    _label_count_map := _label_count_map || jsonb_build_object(_single_col_name, _current_label_count);
    
    if _current_label_count = 1 then
        _unique_col_name := _single_col_name;
    else
        _unique_col_name := _single_col_name || ' (' || _single_col_key || ')';
    end if;
    
    RAISE NOTICE 'Test 3 - Different label: Label=%, Key=%, Unique Name=%, Count=%', 
        _single_col_name, _single_col_key, _unique_col_name, _current_label_count;
    
    -- Test case 4: Third occurrence of "Client ID"
    _single_col_name := 'Client ID';
    _single_col_key := 'key4';
    
    _current_label_count := COALESCE((_label_count_map->>_single_col_name)::integer, 0) + 1;
    _label_count_map := _label_count_map || jsonb_build_object(_single_col_name, _current_label_count);
    
    if _current_label_count = 1 then
        _unique_col_name := _single_col_name;
    else
        _unique_col_name := _single_col_name || ' (' || _single_col_key || ')';
    end if;
    
    RAISE NOTICE 'Test 4 - Third occurrence: Label=%, Key=%, Unique Name=%, Count=%', 
        _single_col_name, _single_col_key, _unique_col_name, _current_label_count;
        
    RAISE NOTICE 'Final label count map: %', _label_count_map;
END $$;
